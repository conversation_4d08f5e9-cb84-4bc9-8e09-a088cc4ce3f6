<?php
// Debug script đơn giản để kiểm tra IMEI
require_once 'application/config/database.php';

// Kết nối database
$host = $db['default']['hostname'];
$username = $db['default']['username'];
$password = $db['default']['password'];
$database = $db['default']['database'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Debug IMEI - Kiểm tra từng bước</h2>";
    
    // Bước 1: Kiểm tra sản phẩm IMEI có sẵn
    echo "<h3>Bước 1: Danh sách sản phẩm IMEI</h3>";
    $stmt = $pdo->query("SELECT id, name, code, type FROM tbl_items WHERE type = 'IMEI_Product' AND del_status = 'Live' LIMIT 10");
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($items)) {
        echo "<p style='color: red;'>Không có sản phẩm IMEI nào trong hệ thống!</p>";
        exit;
    }
    
    foreach ($items as $item) {
        echo "<p>ID: {$item['id']}, Name: {$item['name']}, Code: {$item['code']}</p>";
    }
    
    // Bước 2: Kiểm tra dữ liệu trong tbl_stock_detail
    $test_item_id = $items[0]['id']; // Lấy item đầu tiên để test
    $outlet_id = 1; // Giả sử outlet_id = 1
    
    echo "<h3>Bước 2: Kiểm tra dữ liệu trong tbl_stock_detail cho Item ID: $test_item_id</h3>";
    $stmt = $pdo->query("SELECT id, item_id, expiry_imei_serial, type, outlet_id FROM tbl_stock_detail WHERE item_id = $test_item_id AND type = 1 AND expiry_imei_serial != '' LIMIT 10");
    $stock_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($stock_data)) {
        echo "<p style='color: red;'>Không có dữ liệu IMEI trong tbl_stock_detail cho sản phẩm này!</p>";
    } else {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Item ID</th><th>IMEI</th><th>Type</th><th>Outlet ID</th></tr>";
        foreach ($stock_data as $row) {
            echo "<tr><td>{$row['id']}</td><td>{$row['item_id']}</td><td>{$row['expiry_imei_serial']}</td><td>{$row['type']}</td><td>{$row['outlet_id']}</td></tr>";
        }
        echo "</table>";
    }
    
    // Bước 3: Kiểm tra dữ liệu trong tbl_stock_detail2 (đã bán)
    echo "<h3>Bước 3: Kiểm tra dữ liệu trong tbl_stock_detail2 cho Item ID: $test_item_id</h3>";
    $stmt = $pdo->query("SELECT id, item_id, expiry_imei_serial, type, outlet_id FROM tbl_stock_detail2 WHERE item_id = $test_item_id AND type = 2 AND expiry_imei_serial != '' LIMIT 10");
    $sold_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sold_data)) {
        echo "<p style='color: green;'>Không có IMEI nào đã bán cho sản phẩm này (tốt!)</p>";
    } else {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Item ID</th><th>IMEI</th><th>Type</th><th>Outlet ID</th></tr>";
        foreach ($sold_data as $row) {
            echo "<tr><td>{$row['id']}</td><td>{$row['item_id']}</td><td>{$row['expiry_imei_serial']}</td><td>{$row['type']}</td><td>{$row['outlet_id']}</td></tr>";
        }
        echo "</table>";
    }
    
    // Bước 4: Test truy vấn đơn giản
    echo "<h3>Bước 4: Test truy vấn đơn giản</h3>";
    $sql = "SELECT 
        (
            SELECT GROUP_CONCAT(st.expiry_imei_serial SEPARATOR '||')
            FROM tbl_stock_detail st
            WHERE st.item_id = $test_item_id AND st.type = 1 AND st.expiry_imei_serial != '' AND st.outlet_id = $outlet_id
            AND st.expiry_imei_serial NOT IN (
                SELECT st2.expiry_imei_serial
                FROM tbl_stock_detail2 st2
                WHERE st2.item_id = $test_item_id AND st2.type = 2
                AND st2.expiry_imei_serial != '' AND st2.outlet_id = $outlet_id
            )
        ) as available_imei";
    
    echo "<p><strong>SQL:</strong> $sql</p>";
    
    $stmt = $pdo->query($sql);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Kết quả:</strong> " . ($result['available_imei'] ? $result['available_imei'] : 'Không có IMEI') . "</p>";
    
    if ($result['available_imei']) {
        $imei_list = explode('||', $result['available_imei']);
        echo "<p><strong>Danh sách IMEI có sẵn:</strong></p>";
        echo "<ul>";
        foreach ($imei_list as $imei) {
            if (trim($imei)) {
                echo "<li>" . trim($imei) . "</li>";
            }
        }
        echo "</ul>";
    }
    
    // Bước 5: Test API trực tiếp
    echo "<h3>Bước 5: Test API getIMEISerial</h3>";
    echo "<form method='POST'>";
    echo "<input type='hidden' name='test_api' value='1'>";
    echo "<input type='hidden' name='item_id' value='$test_item_id'>";
    echo "<button type='submit'>Test API với Item ID: $test_item_id</button>";
    echo "</form>";
    
    if (isset($_POST['test_api'])) {
        echo "<h4>Kết quả API:</h4>";
        
        // Simulate API call
        $api_url = "http://localhost:8001/Sale/getIMEISerial";
        $post_data = array('item_id' => $_POST['item_id']);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>HTTP Code:</strong> $http_code</p>";
        echo "<p><strong>Response:</strong></p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        if ($http_code == 200) {
            $json_response = json_decode($response, true);
            if ($json_response) {
                echo "<p><strong>Parsed JSON:</strong></p>";
                echo "<pre>" . print_r($json_response, true) . "</pre>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Lỗi: " . $e->getMessage() . "</p>";
}
?>
