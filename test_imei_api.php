<?php
// Test script để kiểm tra API getIMEISerial
require_once 'application/config/database.php';

// Kết nối database
$host = $db['default']['hostname'];
$username = $db['default']['username'];
$password = $db['default']['password'];
$database = $db['default']['database'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h2>Test IMEI API - Kiểm tra dữ liệu IMEI từ sale return</h2>";

    // Kiểm tra cấu trúc bảng tbl_sale_return_details
    echo "<h3>Cấu trúc bảng tbl_sale_return_details:</h3>";
    $stmt = $pdo->query("DESCRIBE tbl_sale_return_details");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "<p>{$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Default']}</p>";
    }

    // Kiểm tra dữ liệu trong bảng tbl_sale_return_details
    echo "<h3>Dữ liệu trong tbl_sale_return_details:</h3>";
    $stmt = $pdo->query("SELECT * FROM tbl_sale_return_details WHERE expiry_imei_serial != '' AND del_status = 'Live' LIMIT 10");
    $return_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (empty($return_data)) {
        echo "<p style='color: red;'>Không có dữ liệu IMEI trong bảng tbl_sale_return_details</p>";
    } else {
        foreach ($return_data as $row) {
            echo "<p>ID: {$row['id']}, Item ID: {$row['item_id']}, IMEI: {$row['expiry_imei_serial']}, Outlet: {$row['outlet_id']}, Status: {$row['del_status']}</p>";
        }
    }

    // Lấy danh sách sản phẩm IMEI
    $stmt = $pdo->query("SELECT id, name, type FROM tbl_items WHERE type = 'IMEI_Product' AND del_status = 'Live' LIMIT 5");
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h3>Danh sách sản phẩm IMEI:</h3>";
    foreach ($items as $item) {
        echo "<p>ID: {$item['id']}, Name: {$item['name']}, Type: {$item['type']}</p>";

        // Test truy vấn IMEI cho từng sản phẩm
        $item_id = $item['id'];
        $outlet_id = 1; // Giả sử outlet_id = 1
        
        $sql = "SELECT p.name as item_name, p.code as item_code, p.type as item_type,
        (
            SELECT GROUP_CONCAT(DISTINCT combined_imei.expiry_imei_serial SEPARATOR '||')
            FROM (
                /* IMEI từ purchase */
                SELECT st.expiry_imei_serial
                FROM tbl_stock_detail st
                WHERE p.id=st.item_id AND st.type=1 AND st.expiry_imei_serial!='' AND st.outlet_id='$outlet_id'
                AND st.expiry_imei_serial NOT IN (
                    SELECT st2.expiry_imei_serial
                    FROM tbl_stock_detail2 st2
                    WHERE st2.item_id=p.id AND st2.type=2
                    AND st2.expiry_imei_serial!='' AND st2.outlet_id='$outlet_id'
                )
                AND (
                    /* Chỉ lấy IMEI có ID lớn nhất (mới nhất) cho mỗi IMEI */
                    st.id IN (
                        SELECT MAX(st3.id)
                        FROM tbl_stock_detail st3
                        WHERE st3.expiry_imei_serial=st.expiry_imei_serial
                        AND st3.item_id=p.id
                        AND st3.outlet_id='$outlet_id'
                        GROUP BY st3.expiry_imei_serial
                    )
                )
                UNION
                /* IMEI từ sale return */
                SELECT sr.expiry_imei_serial
                FROM tbl_sale_return_details sr
                WHERE sr.item_id=p.id AND sr.expiry_imei_serial!='' AND sr.outlet_id='$outlet_id'
                AND sr.del_status='Live'
            ) as combined_imei
        ) as allimei
        FROM tbl_items p WHERE p.id='$item_id' AND p.del_status='Live'";
        
        $stmt2 = $pdo->query($sql);
        $result = $stmt2->fetch(PDO::FETCH_ASSOC);
        
        echo "<div style='margin-left: 20px;'>";
        echo "<strong>Kết quả truy vấn:</strong><br>";
        echo "All IMEI: " . ($result['allimei'] ? $result['allimei'] : 'Không có IMEI') . "<br>";
        
        // Kiểm tra IMEI từ purchase
        $stmt3 = $pdo->query("SELECT expiry_imei_serial FROM tbl_stock_detail WHERE item_id = $item_id AND type = 1 AND expiry_imei_serial != '' AND outlet_id = $outlet_id");
        $purchase_imei = $stmt3->fetchAll(PDO::FETCH_COLUMN);
        echo "IMEI từ purchase: " . implode(', ', $purchase_imei) . "<br>";
        
        // Kiểm tra IMEI từ sale return
        $stmt4 = $pdo->query("SELECT expiry_imei_serial FROM tbl_sale_return_details WHERE item_id = $item_id AND expiry_imei_serial != '' AND outlet_id = $outlet_id AND del_status = 'Live'");
        $return_imei = $stmt4->fetchAll(PDO::FETCH_COLUMN);
        echo "IMEI từ sale return: " . implode(', ', $return_imei) . "<br>";
        
        echo "</div><hr>";
    }
    
} catch (PDOException $e) {
    echo "Lỗi kết nối database: " . $e->getMessage();
}
?>
