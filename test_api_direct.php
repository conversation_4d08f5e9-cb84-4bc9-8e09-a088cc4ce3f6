<?php
// Test trực tiếp API getIMEISerial
session_start();

// Giả lập session data
$_SESSION['outlet_id'] = 1;
$_SESSION['company_id'] = 1;
$_SESSION['user_id'] = 1;

// Include các file cần thiết
require_once 'application/config/database.php';
require_once 'application/models/Common_model.php';

// Khởi tạo CodeIgniter framework
$CI = &get_instance();
if (!$CI) {
    // Tạo mock CI object
    class MockCI {
        public $db;
        public $session;
        
        public function __construct() {
            // Kết nối database
            $this->load_database();
            $this->load_session();
        }
        
        private function load_database() {
            global $db;
            $host = $db['default']['hostname'];
            $username = $db['default']['username'];
            $password = $db['default']['password'];
            $database = $db['default']['database'];
            
            try {
                $this->db = new PDO("mysql:host=$host;dbname=$database", $username, $password);
                $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            } catch (PDOException $e) {
                die("Database connection failed: " . $e->getMessage());
            }
        }
        
        private function load_session() {
            $this->session = new stdClass();
            $this->session->userdata = function($key) {
                return $_SESSION[$key] ?? null;
            };
        }
        
        public function query($sql) {
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt;
        }
    }
    
    $CI = new MockCI();
}

echo "<h2>Test API getIMEISerial trực tiếp</h2>";

// Test với một item_id cụ thể
$test_item_id = 1; // Thay đổi ID này theo dữ liệu thực tế

echo "<h3>Test với Item ID: $test_item_id</h3>";

// Kiểm tra sản phẩm có tồn tại không
try {
    $stmt = $CI->db->prepare("SELECT id, name, type FROM tbl_items WHERE id = ? AND del_status = 'Live'");
    $stmt->execute([$test_item_id]);
    $item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$item) {
        echo "<p style='color: red;'>Sản phẩm với ID $test_item_id không tồn tại hoặc đã bị xóa</p>";
        
        // Lấy danh sách sản phẩm IMEI có sẵn
        $stmt = $CI->db->query("SELECT id, name, type FROM tbl_items WHERE type = 'IMEI_Product' AND del_status = 'Live' LIMIT 5");
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Danh sách sản phẩm IMEI có sẵn:</h4>";
        foreach ($items as $available_item) {
            echo "<p>ID: {$available_item['id']}, Name: {$available_item['name']}</p>";
        }
        exit;
    }
    
    echo "<p>Sản phẩm: {$item['name']} (Type: {$item['type']})</p>";
    
    // Test truy vấn IMEI
    $outlet_id = $_SESSION['outlet_id'];
    
    $sql = "SELECT p.name as item_name, p.code as item_code, p.type as item_type,
    (
        SELECT GROUP_CONCAT(DISTINCT combined_imei.expiry_imei_serial SEPARATOR '||')
        FROM (
            /* IMEI từ purchase */
            SELECT st.expiry_imei_serial
            FROM tbl_stock_detail st
            WHERE p.id=st.item_id AND st.type=1 AND st.expiry_imei_serial!='' AND st.outlet_id='$outlet_id'
            AND st.expiry_imei_serial NOT IN (
                SELECT st2.expiry_imei_serial
                FROM tbl_stock_detail2 st2
                WHERE st2.item_id=p.id AND st2.type=2
                AND st2.expiry_imei_serial!='' AND st2.outlet_id='$outlet_id'
            )
            AND (
                /* Chỉ lấy IMEI có ID lớn nhất (mới nhất) cho mỗi IMEI */
                st.id IN (
                    SELECT MAX(st3.id)
                    FROM tbl_stock_detail st3
                    WHERE st3.expiry_imei_serial=st.expiry_imei_serial
                    AND st3.item_id=p.id
                    AND st3.outlet_id='$outlet_id'
                    GROUP BY st3.expiry_imei_serial
                )
            )
            UNION
            /* IMEI từ sale return */
            SELECT sr.expiry_imei_serial
            FROM tbl_sale_return_details sr
            WHERE sr.item_id=p.id AND sr.expiry_imei_serial!='' AND sr.outlet_id='$outlet_id'
            AND sr.del_status='Live'
        ) as combined_imei
    ) as allimei
    FROM tbl_items p WHERE p.id='$test_item_id' AND p.del_status='Live'";
    
    echo "<h4>Truy vấn SQL:</h4>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    $stmt = $CI->db->query($sql);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h4>Kết quả:</h4>";
    if ($result) {
        echo "<p><strong>All IMEI:</strong> " . ($result['allimei'] ? $result['allimei'] : 'Không có IMEI') . "</p>";
        
        if ($result['allimei']) {
            $imei_list = explode('||', $result['allimei']);
            echo "<p><strong>Danh sách IMEI:</strong></p>";
            echo "<ul>";
            foreach ($imei_list as $imei) {
                echo "<li>" . trim($imei) . "</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>Không có kết quả trả về</p>";
    }
    
    // Kiểm tra riêng từng phần
    echo "<h4>Kiểm tra chi tiết:</h4>";
    
    // IMEI từ purchase
    $stmt = $CI->db->query("SELECT expiry_imei_serial FROM tbl_stock_detail WHERE item_id = $test_item_id AND type = 1 AND expiry_imei_serial != '' AND outlet_id = $outlet_id");
    $purchase_imei = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p><strong>IMEI từ purchase:</strong> " . (empty($purchase_imei) ? 'Không có' : implode(', ', $purchase_imei)) . "</p>";
    
    // IMEI từ sale return
    $stmt = $CI->db->query("SELECT expiry_imei_serial FROM tbl_sale_return_details WHERE item_id = $test_item_id AND expiry_imei_serial != '' AND outlet_id = $outlet_id AND del_status = 'Live'");
    $return_imei = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p><strong>IMEI từ sale return:</strong> " . (empty($return_imei) ? 'Không có' : implode(', ', $return_imei)) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Lỗi: " . $e->getMessage() . "</p>";
}
?>
