<!DOCTYPE html>
<html>
<head>
    <title>Test POS API</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h2>Test API Sale/getIMEISerial</h2>
    
    <div>
        <label>Item ID:</label>
        <input type="number" id="item_id" value="1" />
        <button onclick="testAPI()">Test API</button>
    </div>
    
    <div id="result" style="margin-top: 20px;"></div>
    
    <script>
        function testAPI() {
            const itemId = document.getElementById('item_id').value;
            
            if (!itemId) {
                alert('Vui lòng nhập Item ID');
                return;
            }
            
            // Test API Sale/getIMEISerial
            $.ajax({
                url: 'Sale/getIMEISerial',
                method: 'POST',
                dataType: 'json',
                data: { 
                    item_id: itemId,
                    item_type: 'IMEI_Product'
                },
                success: function(response) {
                    console.log('API Response:', response);
                    
                    let html = '<h3>Kết quả API:</h3>';
                    html += '<p><strong>Status:</strong> ' + response.status + '</p>';
                    
                    if (response.status === 'success' && response.data) {
                        html += '<p><strong>Item Name:</strong> ' + (response.data.item_name || 'N/A') + '</p>';
                        html += '<p><strong>Item Type:</strong> ' + (response.data.item_type || 'N/A') + '</p>';
                        html += '<p><strong>All IMEI:</strong> ' + (response.data.allimei || 'Không có IMEI') + '</p>';
                        html += '<p><strong>Stock Qty:</strong> ' + (response.data.stock_qty || '0') + '</p>';
                        
                        if (response.data.allimei) {
                            const imeiList = response.data.allimei.split('||');
                            html += '<h4>Danh sách IMEI:</h4>';
                            html += '<ul>';
                            imeiList.forEach(function(imei) {
                                if (imei.trim()) {
                                    html += '<li>' + imei.trim() + '</li>';
                                }
                            });
                            html += '</ul>';
                            
                            // Test dropdown như trong POS
                            html += '<h4>Test Dropdown (như trong POS):</h4>';
                            html += '<select id="test_dropdown">';
                            html += '<option value="">Select IMEI</option>';
                            imeiList.forEach(function(imei) {
                                if (imei.trim()) {
                                    html += '<option value="' + imei.trim() + '">' + imei.trim() + '</option>';
                                }
                            });
                            html += '</select>';
                        }
                    } else if (response.status === 'error') {
                        html += '<p style="color: red;"><strong>Error:</strong> ' + response.message + '</p>';
                    }
                    
                    document.getElementById('result').innerHTML = html;
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    console.error('Response:', xhr.responseText);
                    
                    let html = '<h3>Lỗi AJAX:</h3>';
                    html += '<p style="color: red;"><strong>Status:</strong> ' + status + '</p>';
                    html += '<p style="color: red;"><strong>Error:</strong> ' + error + '</p>';
                    html += '<p style="color: red;"><strong>Response:</strong> ' + xhr.responseText + '</p>';
                    
                    document.getElementById('result').innerHTML = html;
                }
            });
        }
        
        // Test với item ID mặc định khi trang load
        $(document).ready(function() {
            // Lấy danh sách sản phẩm IMEI có sẵn
            $.ajax({
                url: 'test_imei_api.php',
                method: 'GET',
                success: function(response) {
                    console.log('Available items loaded');
                },
                error: function() {
                    console.log('Could not load available items');
                }
            });
        });
    </script>
    
    <hr>
    
    <h3>Hướng dẫn:</h3>
    <ol>
        <li>Nhập Item ID của sản phẩm IMEI_Product</li>
        <li>Click "Test API" để kiểm tra</li>
        <li>Xem kết quả trả về từ API</li>
        <li>Kiểm tra xem có IMEI từ sale return không</li>
    </ol>
    
    <p><strong>Lưu ý:</strong> Để có IMEI từ sale return, bạn cần:</p>
    <ul>
        <li>Tạo một sale với sản phẩm IMEI</li>
        <li>Tạo sale return cho sản phẩm đó</li>
        <li>Sau đó test API này</li>
    </ul>
</body>
</html>
